import React, { useState, useEffect, useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import { Settings, Zap, Lightbulb, Fan, Thermometer } from "lucide-react";
import {
  getUnitIOSpec,
  getOutputTypes,
  getInputFunctions,
  INPUT_FUNCTIONS,
} from "@/constants";
import { useProjectDetail } from "@/contexts/project-detail-context";
import { ScrollArea } from "@/components/ui/scroll-area";

export function IOConfigDialog({ open, onOpenChange, item = null }) {
  const { projectItems } = useProjectDetail();

  const [activeTab, setActiveTab] = useState("input");
  const [lightingItems, setLightingItems] = useState([]);
  const [airconItems, setAirconItems] = useState([]);
  const [inputConfigs, setInputConfigs] = useState([]);
  const [outputConfigs, setOutputConfigs] = useState([]);

  // Get I/O specifications for the unit - memoized to prevent recalculation
  const ioSpec = useMemo(() => {
    return item?.type ? getUnitIOSpec(item.type) : null;
  }, [item?.type]);

  const outputTypes = useMemo(() => {
    return item?.type ? getOutputTypes(item.type) : [];
  }, [item?.type]);

  // Load lighting and aircon data from projectItems
  useEffect(() => {
    if (projectItems?.lighting) {
      setLightingItems(projectItems.lighting);
    }
    if (projectItems?.aircon) {
      setAirconItems(projectItems.aircon);
    }
  }, [projectItems]);

  // Helper function to get output label (moved outside useEffect to avoid dependency issues)
  const getOutputLabel = (type) => {
    switch (type) {
      case "relay":
        return "Relay";
      case "dimmer":
        return "Dimmer";
      case "ao":
        return "AO";
      case "ac":
        return "AC";
      default:
        return type;
    }
  };

  // Helper function to generate input name based on unit type and WinForms pattern
  const getInputName = (unitType, index) => {
    // Units that use "Input 1.1, 1.2, 1.3" format (based on WinForms code)
    const dotFormatUnits = [
      "RCU-48IN-16RL",
      "RCU-30IN-10RL",
      "RCU-48IN-16RL-4AO",
    ];

    if (dotFormatUnits.includes(unitType)) {
      return `Input 1.${index + 1}`;
    }

    // Default format: "Input 1, Input 2, Input 3"
    return `Input ${index + 1}`;
  };

  // Initialize input/output configurations
  useEffect(() => {
    if (!ioSpec) return;

    // Initialize input configurations
    const inputs = [];
    for (let i = 0; i < ioSpec.inputs; i++) {
      inputs.push({
        index: i,
        name: getInputName(item?.type, i),
        lightingId: null,
        functionValue: 0, // Default to "Unused"
      });
    }
    setInputConfigs(inputs);

    // Initialize output configurations
    const outputs = [];
    let outputIndex = 0;

    outputTypes.forEach(({ type, count }) => {
      for (let i = 0; i < count; i++) {
        outputs.push({
          index: outputIndex++,
          name: `${getOutputLabel(type)} ${i + 1}`,
          type: type,
          deviceId: null,
        });
      }
    });
    setOutputConfigs(outputs);
  }, [ioSpec, outputTypes, item?.type]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    // TODO: Implement I/O configuration save logic
    console.log("I/O Config for unit:", item);
    console.log("Input configs:", inputConfigs);
    console.log("Output configs:", outputConfigs);
    handleClose();
  };

  const getOutputIcon = (type) => {
    switch (type) {
      case "relay":
        return <Zap className="h-4 w-4" />;
      case "dimmer":
        return <Lightbulb className="h-4 w-4" />;
      case "ao":
        return <Fan className="h-4 w-4" />;
      case "ac":
        return <Thermometer className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  // Helper functions for input/output configuration
  const handleInputLightingChange = (inputIndex, lightingId) => {
    setInputConfigs((prev) =>
      prev.map((config) =>
        config.index === inputIndex ? { ...config, lightingId } : config
      )
    );
  };

  const handleInputFunctionChange = (inputIndex, functionValue) => {
    setInputConfigs((prev) =>
      prev.map((config) =>
        config.index === inputIndex ? { ...config, functionValue } : config
      )
    );
  };

  const handleOutputDeviceChange = (outputIndex, deviceId) => {
    setOutputConfigs((prev) =>
      prev.map((config) =>
        config.index === outputIndex ? { ...config, deviceId } : config
      )
    );
  };

  // Prepare combobox options - memoized to prevent recalculation
  const lightingOptions = useMemo(() => {
    return lightingItems.map((item) => ({
      value: item.id.toString(),
      label: `${item.name || "Unnamed"} (${item.address})`,
    }));
  }, [lightingItems]);

  const airconOptions = useMemo(() => {
    return airconItems.map((item) => ({
      value: item.id.toString(),
      label: `${item.name || "Unnamed"} (${item.address})`,
    }));
  }, [airconItems]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[70%] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            I/O Configuration
          </DialogTitle>
          <DialogDescription>
            Configure input/output settings for {item?.type || "unit"}:{" "}
            {item?.serial_no || "N/A"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {ioSpec ? (
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="input">Input Configuration</TabsTrigger>
                <TabsTrigger value="output">Output Configuration</TabsTrigger>
              </TabsList>

              {/* Input Tab */}
              <TabsContent value="input" className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Input Configuration</h3>
                  <Badge variant="secondary" className="ml-auto">
                    {ioSpec.inputs} Inputs
                  </Badge>
                </div>
                <ScrollArea className="max-h-[500px] overflow-auto p-2 pl-0">
                  {inputConfigs.length > 0 ? (
                    <div className="space-y-3">
                      {inputConfigs.map((config) => {
                        const availableFunctions = getInputFunctions(
                          item?.type,
                          config.index
                        );
                        const functionOptions = availableFunctions.map(
                          (func) => ({
                            value: func.value.toString(),
                            label: func.label,
                          })
                        );

                        return (
                          <div
                            key={config.index}
                            className="grid grid-cols-3 gap-4 p-4 border rounded-lg"
                          >
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">
                                {config.name}
                              </Label>
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm text-muted-foreground">
                                Lighting
                              </Label>
                              <Combobox
                                options={lightingOptions}
                                value={config.lightingId?.toString() || ""}
                                onValueChange={(value) =>
                                  handleInputLightingChange(
                                    config.index,
                                    value ? parseInt(value) : null
                                  )
                                }
                                placeholder="Select lighting..."
                                emptyText="No lighting found"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm text-muted-foreground">
                                Function
                              </Label>
                              <Combobox
                                options={functionOptions}
                                value={config.functionValue.toString()}
                                onValueChange={(value) =>
                                  handleInputFunctionChange(
                                    config.index,
                                    parseInt(value)
                                  )
                                }
                                placeholder="Select function..."
                                emptyText="No functions available"
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      <p>No inputs available for this unit type.</p>
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>

              {/* Output Tab */}
              <TabsContent value="output" className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">
                    Output Configuration
                  </h3>
                  <Badge variant="secondary" className="ml-auto">
                    {ioSpec.totalOutputs} Outputs
                  </Badge>
                </div>

                {outputConfigs.length > 0 ? (
                  <div className="space-y-3">
                    {outputConfigs.map((config) => {
                      const isAircon = config.type === "ac";
                      const deviceOptions = isAircon
                        ? airconOptions
                        : lightingOptions;

                      return (
                        <div
                          key={config.index}
                          className="grid grid-cols-2 gap-4 p-4 border rounded-lg"
                        >
                          <div className="space-y-2">
                            <Label className="text-sm font-medium flex items-center gap-2">
                              {getOutputIcon(config.type)}
                              {config.name}
                            </Label>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm text-muted-foreground">
                              {isAircon ? "Air Conditioner" : "Lighting"}
                            </Label>
                            <Combobox
                              options={deviceOptions}
                              value={config.deviceId?.toString() || ""}
                              onValueChange={(value) =>
                                handleOutputDeviceChange(
                                  config.index,
                                  value ? parseInt(value) : null
                                )
                              }
                              placeholder={`Select ${
                                isAircon ? "aircon" : "lighting"
                              }...`}
                              emptyText={`No ${
                                isAircon ? "aircon" : "lighting"
                              } found`}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <p>No outputs available for this unit type.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <div className="text-center text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No I/O Specifications</p>
              <p className="text-sm">
                Unable to load I/O specifications for this unit type.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Configuration</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
